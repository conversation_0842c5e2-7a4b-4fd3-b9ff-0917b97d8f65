"""
对话记录模型
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, UniqueConstraint
from sqlalchemy.sql import func
from .database import Base


class Conversation(Base):
    """对话记录表"""

    __tablename__ = "conversations"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    from_user = Column(
        String(100), index=True, nullable=False, comment="发送用户微信ID"
    )
    conversation_id = Column(String(100), nullable=False, comment="Dify对话ID")
    group = Column(String(100), nullable=False, comment="群组ID")
    hour = Column(String(20), nullable=False, comment="小时标识(YYYYMMDD_HH)")

    # 对话内容（JSON格式）
    content = Column(
        Text,
        nullable=True,
        comment='对话内容JSON格式，如[{"user":"你好","ai":"你好有什么能帮助你的"}]',
    )

    # 消息状态
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    is_sent = Column(Boolean, default=False, comment="是否已发送回复")

    # 时间戳
    question_time = Column(DateTime, default=func.now(), comment="提问时间")
    answer_time = Column(DateTime, nullable=True, comment="回答时间")
    sent_time = Column(DateTime, nullable=True, comment="发送时间")

    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(
        DateTime, default=func.now(), onupdate=func.now(), comment="更新时间"
    )

    # 添加四字段组合的唯一性约束
    __table_args__ = (
        UniqueConstraint('from_user', 'conversation_id', 'group', 'hour',
                         name='uq_conversation_key'),
    )

    def __repr__(self):
        return f"<Conversation(from_user='{self.from_user}', conversation_id='{self.conversation_id}', group='{self.group}', hour='{self.hour}')>"
