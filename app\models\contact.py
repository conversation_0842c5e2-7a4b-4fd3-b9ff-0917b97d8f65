"""
联系人信息模型
"""
from sqlalchemy import Column, String, Integer, DateTime, Text
from sqlalchemy.sql import func
from .database import Base


class Contact(Base):
    """联系人信息表"""
    __tablename__ = "contacts"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    wc_id = Column(String(100), unique=True, index=True, nullable=False, comment="微信ID/群ID")
    user_name = Column(String(100), nullable=True, comment="微信用户名")
    nick_name = Column(String(100), nullable=True, comment="昵称")
    remark = Column(String(100), nullable=True, comment="备注")
    signature = Column(Text, nullable=True, comment="签名")
    sex = Column(Integer, nullable=True, comment="性别")
    alias_name = Column(String(100), nullable=True, comment="微信号")
    country = Column(String(50), nullable=True, comment="国家")
    big_head = Column(String(500), nullable=True, comment="大头像URL")
    small_head = Column(String(500), nullable=True, comment="小头像URL")
    label_list = Column(Text, nullable=True, comment="标签列表")
    v1 = Column(String(200), nullable=True, comment="v1数据")
    
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<Contact(wc_id='{self.wc_id}', nick_name='{self.nick_name}')>"
