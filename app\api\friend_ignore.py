"""
好友忽略列表管理API
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Set
from loguru import logger

from app.services.friend_ignore_service import friend_ignore_service
from app.services.contact_sync import contact_sync_service
from config import settings


router = APIRouter()


class AddFriendsRequest(BaseModel):
    """添加好友到忽略列表请求模型"""
    friends: List[str]


class RemoveFriendRequest(BaseModel):
    """从忽略列表移除好友请求模型"""
    w_id: str


class IgnoreListResponse(BaseModel):
    """忽略列表响应模型"""
    success: bool
    message: str
    data: Set[str] = None
    count: int = 0


@router.get("/ignore-list", response_model=IgnoreListResponse)
async def get_ignore_list():
    """
    获取当前的好友忽略列表
    
    Returns:
        忽略列表响应
    """
    try:
        ignore_list = friend_ignore_service.get_ignore_list()
        count = friend_ignore_service.get_ignore_list_count()
        
        return IgnoreListResponse(
            success=True,
            message="获取忽略列表成功",
            data=ignore_list,
            count=count
        )
    except Exception as e:
        logger.error(f"获取忽略列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取忽略列表失败: {str(e)}")


@router.post("/ignore-list/add", response_model=IgnoreListResponse)
async def add_friends_to_ignore_list(request: AddFriendsRequest):
    """
    添加好友到忽略列表
    
    Args:
        request: 添加好友请求
        
    Returns:
        操作结果
    """
    try:
        success = friend_ignore_service.add_friends_to_ignore_list(request.friends)
        
        if success:
            count = friend_ignore_service.get_ignore_list_count()
            return IgnoreListResponse(
                success=True,
                message=f"成功添加 {len(request.friends)} 个好友到忽略列表",
                count=count
            )
        else:
            raise HTTPException(status_code=400, detail="添加好友到忽略列表失败")
            
    except Exception as e:
        logger.error(f"添加好友到忽略列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加好友到忽略列表失败: {str(e)}")


@router.post("/ignore-list/remove", response_model=IgnoreListResponse)
async def remove_friend_from_ignore_list(request: RemoveFriendRequest):
    """
    从忽略列表中移除好友
    
    Args:
        request: 移除好友请求
        
    Returns:
        操作结果
    """
    try:
        success = friend_ignore_service.remove_friend_from_ignore_list(request.w_id)
        
        if success:
            count = friend_ignore_service.get_ignore_list_count()
            return IgnoreListResponse(
                success=True,
                message=f"成功从忽略列表中移除好友: {request.w_id}",
                count=count
            )
        else:
            raise HTTPException(status_code=400, detail="从忽略列表移除好友失败")
            
    except Exception as e:
        logger.error(f"从忽略列表移除好友失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"从忽略列表移除好友失败: {str(e)}")


@router.delete("/ignore-list", response_model=IgnoreListResponse)
async def clear_ignore_list():
    """
    清空忽略列表
    
    Returns:
        操作结果
    """
    try:
        success = friend_ignore_service.clear_ignore_list()
        
        if success:
            return IgnoreListResponse(
                success=True,
                message="成功清空忽略列表",
                count=0
            )
        else:
            raise HTTPException(status_code=400, detail="清空忽略列表失败")
            
    except Exception as e:
        logger.error(f"清空忽略列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空忽略列表失败: {str(e)}")


@router.post("/sync-contacts", response_model=IgnoreListResponse)
async def sync_contacts_and_rebuild_ignore_list():
    """
    重新同步联系人并重建忽略列表
    
    Returns:
        操作结果
    """
    try:
        if not settings.ecloud_w_id:
            raise HTTPException(status_code=400, detail="未配置ecloud_w_id")
            
        success = contact_sync_service.sync_contacts_on_startup(settings.ecloud_w_id)
        
        if success:
            count = friend_ignore_service.get_ignore_list_count()
            return IgnoreListResponse(
                success=True,
                message="联系人同步完成，忽略列表已重建",
                count=count
            )
        else:
            raise HTTPException(status_code=400, detail="联系人同步失败")
            
    except Exception as e:
        logger.error(f"联系人同步失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"联系人同步失败: {str(e)}")


@router.get("/ignore-list/check/{w_id}")
async def check_if_friend_ignored(w_id: str):
    """
    检查指定w_id的详细忽略状态

    Args:
        w_id: 要检查的w_id

    Returns:
        详细的检查结果
    """
    try:
        status_info = friend_ignore_service.get_ignore_status_info(w_id)

        return {
            "success": True,
            "data": status_info,
            "message": f"w_id {w_id} 状态检查完成"
        }

    except Exception as e:
        logger.error(f"检查忽略状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查忽略状态失败: {str(e)}")


@router.get("/whitelist")
async def get_whitelist():
    """
    获取当前的白名单列表

    Returns:
        白名单列表
    """
    try:
        whitelist = friend_ignore_service.get_whitelist()

        return {
            "success": True,
            "data": whitelist,
            "count": len(whitelist),
            "message": "获取白名单成功"
        }

    except Exception as e:
        logger.error(f"获取白名单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取白名单失败: {str(e)}")


@router.get("/config")
async def get_ignore_config():
    """
    获取好友忽略功能的配置信息

    Returns:
        配置信息
    """
    try:
        return {
            "success": True,
            "data": {
                "ignore_enabled": settings.friend_ignore_enabled,
                "whitelist": settings.friend_ignore_whitelist,
                "whitelist_count": len(settings.friend_ignore_whitelist),
                "ignore_list_count": friend_ignore_service.get_ignore_list_count()
            },
            "message": "获取配置信息成功"
        }

    except Exception as e:
        logger.error(f"获取配置信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置信息失败: {str(e)}")
