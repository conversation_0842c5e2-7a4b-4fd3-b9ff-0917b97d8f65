# 获取联系人信息接口
简要描述：
获取联系人信息

请求URL：
http://域名地址/getContact

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：{Authorization}

参数：

参数名	必选	类型	说明
wId	是	String	登录实例标识
wcId	是	String	好友微信id/群id，多个使用英文逗号分隔

请求参数示例


{
    "wId": "349be9b5-8734-45ce-811d-4e10ca568c67",
    "wcId": "wxid_wl9qchkanp9u22"
}
成功返回示例

{
    "message": "成功",
    "code": "1000",
    "data": [
        {
            "userName": "test558666",
            "nickName": "追风少年666",
            "remark": "",
            "signature": "66666",
            "sex": 1,
            "aliasName": "test558666",
            "country": "CN",
            "bigHead": "http://wx.qlogo.cn/mmhead/PiajxSqBRaEL8iaRQBnStn37LYat3fREC4Y2iaStECzbX3icxntWBhWQ3w/0",
            "smallHead": "http://wx.qlogo.cn/mmhead/PiajxSqBRaEL8iaRQBnStn37LYat3fREC4Y2iaStECzbX3icxntWBhWQ3w/132",
            "labelList": "",
            "v1": "v1_584e7774024c79af0e7304bf7afba775b31bf075651c16c964b1b5bf16369924ebf1ee7bc151c1feee1979e1dd40f0dd@stranger"
        }
    ]
}
错误返回示例

{
    "message": "失败",
    "code": "1001",
    "data": null
}
返回数据：

参数名	类型	说明
code	String	1000成功
1001失败
msg	String	反馈信息
data	JSONObject	
userName	String	微信id
nickName	String	昵称
remark	String	备注
signature	String	签名
sex	int	性别
aliasName	String	微信号
country	String	国家
bigHead	String	大头像
smallHead	String	小头像
labelList	String	标签列表
v1	String	用户的wxId，都是以v1开头的一串数值，v2数据，则是作为v1数据的辅助


# 接收回调消息
由于重新登录/服务器重启/网络重放等原因，消息可能存在重复推送历史消息，调用方必须进行消息排重处理。例如以newMsgId字段/timestamp时间戳作为逻辑处理

获取回调消息字段说明
wcId	String	微信id
account	String	账号
messageType	String	消息类型
data	JSONObject	消息体
data.fromUser	String	发送微信id
data.fromGroup	String	发送群号
data.toUser	String	接收微信id
data.msgId	long	消息msgId
data.newMsgId	long	消息newMsgId
data.timestamp	long	时间
data.content	String	消息体
data.self	boolean	是否是自己发送的消息

下面为示例
{
    "account": "***********",
    "data": {
        "content": "消息内容",
        "fromGroup": "***********@chatroom",
        "fromUser": "wxid_ynu1tgjz156j22",
        "memberCount": 8,
        "msgId": **********,
        "atlist": ["wxid_phyyedw9xap22"],
        "newMsgId": 1497474118261484795,
        "self": false,
        "timestamp": **********,
        "toUser": "wxid_phyyedw9xap22",
        "wId": "12491ae9-62aa-4f7a-83e6-9db4e9f28e3c"
    },
    "messageType": "80001",
    "wcId": "wxid_phyyedw9xap22"
}

# 发送文本消息
请求URL：
http://域名地址/sendText

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：{Authorization}

参数：
参数名	必选	类型	说明
wId	是	string	登录实例标识
wcId	是	string	接收人微信id/群id
content	是	string	文本内容消息

返回数据：

参数名	类型	说明
code	string	1000成功，1001失败
msg	string	反馈信息
data		
data.type	int	类型
data.msgId	long	消息msgId
data.newMsgId	long	消息newMsgId
data.createTime	long	消息发送时间戳
data.wcId	string	消息接收方id

请求参数示例

------------------------ 好友消息 ------------------------------
{
    "wId": "0000016e-63eb-f319-0001-ed01076abf1f",
    "wcId": "azhichao",
    "content": "天行健，君子以自强不息"
}

成功返回示例

{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "type": 1,
        "msgId": 2562652205,
        "newMsgId": 4482117376572170921,
        "createTime": 1641457769,
        "wcId": "azhichao"
    }
}
错误返回示例

{
    "message": "失败",
    "code": "1001",
    "data": null
} 



# 初始化通讯录列表接口

简要描述：
初始化通讯录列表

请求URL：
http://域名地址/initAddressList

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：{Authorization}

参数：
参数名	必选	类型	说明
wId	是	String	登录实例标识

请求参数示例
{
    "wId": "6a696578-16ea-4edc-ac8b-e609bca39c69"
}

成功返回示例
{
    "message": "成功",
    "code": "1000",
    "data": null
}

错误返回示例
{
    "message": "失败",
    "code": "1001",
    "data": null
}

返回数据：
参数名	类型	说明
code	string	1000成功、1001失败
msg	string	反馈信息
data	JSONObject	无



# 获取通讯录列表接口
简要描述：
获取通讯录列表

请求URL：
http://域名地址/getAddressList

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：{Authorization}

参数：

参数名	必选	类型	说明
wId	是	String	登录实例标识
小提示：
获取通讯录列表之前，必须调用初始化通讯录列表接口。

请求参数示例
{
    "wId": "6a696578-16ea-4edc-ac8b-e609bca39c69"
}

成功返回示例
{
    "code": "1000",
    "message": "获取通讯录成功",
    "data": {
        "chatrooms": [
            ""
        ],
        "friends": [
            ""
        ],
        "ghs": [
            ""
        ],
        "others": [
            ""
        ]
    }
}

错误返回示例
{
    "message": "失败",
    "code": "1001",
    "data": null
}

返回数据：

参数名	类型	说明
code	String	1000成功
1001失败
msg	String	反馈信息
data	JSONObject	
chatrooms	JSONArray	群组列表
friends	JSONArray	好友列表
ghs	JSONArray	公众号列表
others	JSONArray	微信其他相关



# 群聊@接口
请求URL：
http://域名地址/sendText

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：{Authorization}

参数：
参数名	必选	类型	说明
wId	是	string	登录实例标识
wcId	是	string	接收方群id
content	是	string	文本内容消息（@的微信昵称需要自己拼接，必须拼接艾特符号，不然不生效）
at	是	string	艾特的微信id（多个以逗号分开）

返回数据：
参数名	类型	说明
code	string	1000成功，1001失败
msg	string	反馈信息
data		
data.type	int	类型
data.msgId	long	消息msgId
data.newMsgId	long	消息newMsgId
data.createTime	long	消息发送时间戳
data.wcId	string	消息接收方id

请求参数示例
{
 "wId": "0000016f-8911-484a-0001-db2943fc2786",
 "wcId": "22270365143@chatroom",
 "at": "wxid_lr6j4nononb921,wxid_i6qsbbjenjuj22",
 "content": "@E云Team_Mr Li@你微笑时真美 测试"
}

成功返回示例
{
    "code": "1000",
    "message": "处理成功",
    "data": {
        "type": 1,
        "msgId": 2562652205,
        "newMsgId": 4482117376572170921,
        "createTime": 1641457769,
        "wcId": "22270365143@chatroom"
    }
}

错误返回示例
{
    "message": "失败",
    "code": "1001",
    "data": null
}


# 查询账号中在线的微信列表

简要描述：
此接口应用场景是查询在线的wid和wcid列表

请求URL：
http://域名地址/queryLoginWx

请求方式：
POST

请求头Headers：
Content-Type：application/json
Authorization：[Authorization]

无参数：

返回数据：
参数名	类型	说明
code	string	1000成功（在线），1001失败（离线）
message	string	反馈信息
wcId	string	微信id
wId	string	登录实例标识
请求参数示例

{    

}
成功返回示例

{
    "code": "1000",
    "message": "成功",
    "data": [
        {
            "wcId": "wxid_i6qsbbjenju2",
            "wId": "72223018-7f2a-4f4f-bfa3-26e47dbd61"
        }
    ]
}
失败返回示例

{
    "code": "1001",
    "message": "失败"
}