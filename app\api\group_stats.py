"""
群组统计API接口
"""

from typing import Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from loguru import logger

from app.services.group_stats_service import group_stats_service


router = APIRouter()


class GroupStatsResponse(BaseModel):
    """群组统计响应模型"""
    success: bool
    message: str
    data: dict


class ClearStatsRequest(BaseModel):
    """清空统计请求模型"""
    group_id: str


@router.get("/group-stats/{group_id}", response_model=GroupStatsResponse)
async def get_group_stats(group_id: str):
    """
    获取指定群组的统计信息

    Args:
        group_id: 群组ID

    Returns:
        群组统计信息
    """
    try:
        stats_summary = group_stats_service.get_group_stats_summary(group_id)
        
        return GroupStatsResponse(
            success=True,
            message=f"获取群组统计信息成功: {group_id}",
            data=stats_summary
        )

    except Exception as e:
        logger.error(f"获取群组统计信息失败: group_id={group_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取群组统计信息失败: {str(e)}")


@router.get("/group-stats/{group_id}/most-active", response_model=GroupStatsResponse)
async def get_most_active_user(group_id: str):
    """
    获取指定群组中发言次数最多的用户昵称

    Args:
        group_id: 群组ID

    Returns:
        最活跃用户信息
    """
    try:
        most_active_nickname = group_stats_service.get_most_active_user_nickname(group_id)
        message_stats = group_stats_service.get_group_message_stats(group_id)
        
        # 找到最活跃用户的详细信息
        most_active_user_id = None
        max_count = 0
        
        for user_id, count in message_stats.items():
            if count > max_count:
                max_count = count
                most_active_user_id = user_id
        
        data = {
            "group_id": group_id,
            "most_active_nickname": most_active_nickname,
            "most_active_user_id": most_active_user_id,
            "message_count": max_count,
            "total_users": len([count for count in message_stats.values() if count > 0])
        }
        
        return GroupStatsResponse(
            success=True,
            message=f"获取最活跃用户成功: {group_id}",
            data=data
        )

    except Exception as e:
        logger.error(f"获取最活跃用户失败: group_id={group_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最活跃用户失败: {str(e)}")


@router.post("/group-stats/clear", response_model=GroupStatsResponse)
async def clear_group_stats(request: ClearStatsRequest):
    """
    清空指定群组的统计数据

    Args:
        request: 清空统计请求

    Returns:
        操作结果
    """
    try:
        success = group_stats_service.clear_group_stats(request.group_id)
        
        if success:
            return GroupStatsResponse(
                success=True,
                message=f"群组统计数据已清空: {request.group_id}",
                data={"group_id": request.group_id, "cleared": True}
            )
        else:
            raise HTTPException(status_code=400, detail=f"清空群组统计数据失败: {request.group_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空群组统计数据失败: group_id={request.group_id}, error={str(e)}")
        raise HTTPException(status_code=500, detail=f"清空群组统计数据失败: {str(e)}")


@router.get("/group-stats", response_model=GroupStatsResponse)
async def get_all_groups_overview():
    """
    获取所有群组的统计概览

    Returns:
        所有群组的统计概览
    """
    try:
        # 这里可以扩展为获取所有群组的统计信息
        # 目前返回基本信息
        data = {
            "message": "群组统计功能已启用",
            "endpoints": {
                "get_group_stats": "/api/v1/group-stats/{group_id}",
                "get_most_active": "/api/v1/group-stats/{group_id}/most-active",
                "clear_stats": "/api/v1/group-stats/clear"
            }
        }
        
        return GroupStatsResponse(
            success=True,
            message="获取群组统计概览成功",
            data=data
        )

    except Exception as e:
        logger.error(f"获取群组统计概览失败: error={str(e)}")
        raise HTTPException(status_code=500, detail=f"获取群组统计概览失败: {str(e)}")
