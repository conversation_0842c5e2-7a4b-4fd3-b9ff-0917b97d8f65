# E云管家-DifyAI对接服务

这是一个用于将E云管家消息转发到DifyAI并返回AI回答的后端服务程序。

## 功能特性

- 接收E云管家的群聊消息回调
- 过滤和验证消息（仅处理群聊消息，忽略自己发送的消息）
- 使用Redis队列管理用户消息，防止并发处理
- 自动获取和保存联系人信息
- **支持DifyAI流式和阻塞两种模式**
  - 流式模式：实时接收AI回复，响应更快
  - 阻塞模式：等待完整回复后返回，更稳定
- 将AI回答发送回群聊
- 完整的日志记录和错误处理

## 系统架构

```
E云管家 -> 回调接口 -> Redis队列 -> 后台工作进程 -> DifyAI -> 回复消息
                                      ↓
                                   MySQL数据库
```

## 技术栈

- **后端框架**: FastAPI
- **数据库**: MySQL + SQLAlchemy
- **缓存/队列**: Redis
- **日志**: Loguru
- **部署**: Docker + Docker Compose

## 快速开始

### 1. 环境准备

确保已安装以下软件：
- Python 3.11+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7+

### 2. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下参数：
```env
# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/ecloud_dify

# Redis配置
REDIS_URL=redis://localhost:6379/0

# E云管家配置
ECLOUD_BASE_URL=http://your-ecloud-domain.com
ECLOUD_AUTHORIZATION=your-authorization-token

# DifyAI配置
DIFY_BASE_URL=https://api.dify.ai/v1
DIFY_API_KEY=your-dify-api-key
```

### 3. 使用Docker部署（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f app
```

### 4. 手动部署

```bash
# 安装依赖
pip install -r requirements.txt

# 启动MySQL和Redis（或使用Docker）
docker-compose up -d mysql redis

# 初始化数据库
python app/utils/database_init.py

# 启动应用
python main.py
```

## API接口

### 回调接口

**POST** `/api/v1/callback`

接收E云管家的消息回调。

请求体示例：
```json
{
    "account": "***********",
    "messageType": "80001",
    "wcId": "wxid_phyyedw9xap22",
    "data": {
        "content": "消息内容",
        "fromGroup": "***********@chatroom",
        "fromUser": "wxid_ynu1tgjz156j22",
        "memberCount": 8,
        "msgId": **********,
        "newMsgId": 1497474118261484795,
        "self": false,
        "timestamp": **********,
        "toUser": "wxid_phyyedw9xap22",
        "wId": "12491ae9-62aa-4f7a-83e6-9db4e9f28e3c"
    }
}
```

### 健康检查

**GET** `/api/v1/health`

检查服务运行状态。

## 消息处理流程

1. **消息过滤**: 检查是否为群聊消息（messageType=80001）且非自己发送
2. **入队处理**: 将消息加入用户专属的Redis队列
3. **联系人管理**: 检查并获取群组联系人信息
4. **AI对话**: 调用DifyAI接口获取回答
5. **回复发送**: 将AI回答发送回群聊
6. **记录保存**: 保存对话记录到数据库

## 数据库表结构

### contacts（联系人表）
- `id`: 主键
- `wc_id`: 微信ID/群ID（唯一）
- `nick_name`: 昵称
- `user_name`: 微信用户名
- 其他联系人信息字段...

### conversations（对话记录表）
- `id`: 主键
- `from_user`: 发送用户微信ID
- `conversation_id`: Dify对话ID
- `group`: 群组ID
- `hour`: 小时标识(YYYYMMDD_HH)
- `content`: 对话内容(JSON格式)
- `is_processed`: 是否已处理
- `is_sent`: 是否已发送
- 唯一性约束: (from_user, conversation_id, group, hour)

## 配置说明

主要配置项在 `config.json` 中：

### DifyAI配置
- `dify.streaming_enabled`: 是否启用流式模式（默认：false）
- `dify.streaming_timeout`: 流式请求超时时间，单位秒（默认：120）
- `dify.base_url`: DifyAI API地址
- `dify.api_key`: DifyAI API密钥

### 消息处理配置
- `message_processing.max_retry_count`: 最大重试次数（默认3次）
- `message_processing.retry_delay`: 重试延迟（默认5秒）
- `message_processing.queue_timeout`: 队列超时时间（默认300秒）

### 流式模式说明
**启用流式模式的优势：**
- 实时响应：边生成边返回，用户体验更好
- 更快感知：无需等待完整回复即可开始处理
- 连接保活：自动处理ping事件，保持连接稳定

**配置示例：**
```json
{
  "dify": {
    "streaming_enabled": true,
    "streaming_timeout": 180
  }
}
```

## 日志管理

日志文件位置：
- 应用日志: `logs/app.log`
- 错误日志: `logs/app_error.log`

日志级别可通过环境变量 `LOG_LEVEL` 配置。

## 监控和维护

### 查看队列状态
```bash
# 连接Redis查看队列
redis-cli
> KEYS ecloud_queue:*
> LLEN ecloud_queue:用户ID
```

### 查看处理状态
```bash
> KEYS ecloud_processing:*
> GET ecloud_processing:用户ID
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置和网络连接
   - 确认数据库用户权限

2. **Redis连接失败**
   - 检查Redis服务状态
   - 确认Redis配置

3. **DifyAI调用失败**
   - 检查API密钥是否正确
   - 确认网络连接和API配额

4. **E云管家回调失败**
   - 检查回调URL配置
   - 确认服务端口开放

## 开发指南

### 项目结构
```
├── app/
│   ├── api/          # API接口
│   ├── models/       # 数据模型
│   ├── services/     # 业务服务
│   ├── utils/        # 工具函数
│   └── workers/      # 后台工作进程
├── logs/             # 日志文件
├── tests/            # 测试文件
├── config.py         # 配置文件
├── main.py           # 应用入口
└── requirements.txt  # 依赖包
```

### 运行测试
```bash
pytest tests/
```

## 许可证

MIT License
