# -*- mode: python ; coding: utf-8 -*-
import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取项目根目录
project_root = os.path.abspath('.')

# 收集所有数据文件
datas = []

# 添加配置文件
datas.append(('config.example.json', '.'))
if os.path.exists('config.json'):
    datas.append(('config.json', '.'))

# 添加日志目录
if os.path.exists('logs'):
    datas.append(('logs', 'logs'))

# 收集FastAPI和其他包的数据文件
datas.extend(collect_data_files('fastapi'))
datas.extend(collect_data_files('uvicorn'))
datas.extend(collect_data_files('pydantic'))
datas.extend(collect_data_files('sqlalchemy'))

# 收集隐藏导入
hiddenimports = []
hiddenimports.extend(collect_submodules('uvicorn'))
hiddenimports.extend(collect_submodules('fastapi'))
hiddenimports.extend(collect_submodules('pydantic'))
hiddenimports.extend(collect_submodules('sqlalchemy'))
hiddenimports.extend(collect_submodules('pymysql'))
hiddenimports.extend(collect_submodules('redis'))
hiddenimports.extend(collect_submodules('celery'))
hiddenimports.extend(collect_submodules('loguru'))
hiddenimports.extend(collect_submodules('cryptography'))
hiddenimports.extend(collect_submodules('requests'))
hiddenimports.extend(collect_submodules('httpx'))

# 添加特定的隐藏导入
hiddenimports.extend([
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.protocols.websockets.websockets_impl',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.http.h11_impl',
    'uvicorn.protocols.http.httptools_impl',
    'uvicorn.loops.auto',
    'uvicorn.loops.asyncio',
    'uvicorn.logging',
    'pymysql.converters',
    'pymysql.cursors',
    'pymysql.connections',
    'sqlalchemy.dialects.mysql.pymysql',
    'sqlalchemy.pool',
    'sqlalchemy.engine.default',
    'app.api.callback',
    'app.models.database',
    'app.workers.message_worker',
    'app.services.message_processor',
    'app.services.dify_client',
    'app.services.ecloud_client',
    'app.services.redis_queue',
    'app.utils.logger',
])

# 排除不需要的模块
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'test',
    'tests',
]

block_cipher = None

a = Analysis(
    ['startup.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ecloud_dify',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
