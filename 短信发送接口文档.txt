接口地址
https://smsapi.izjun.com:8443/

# 1.前言
本协议基于HTTP服务，使用POST请求方式，请求和应答均为JSON格式数据.。
字段命名方式：驼峰法。
统一请求和响应编码：UTF-8
统一请求Header内容：Content-Type:application/json
请使用接口网关地址替换文档中的服务器地址：http://{address:port}/sms
 sign参数计算规则：多个指定参数值组合成字符串后计算MD532位小写结果
要求：MD5(userName+timestamp+MD5(password))
假设：userName(帐号名)=test
 password(帐号密码)=123
 timestamp=1596254400000
计算：MD5(password)=202cb962ac59075b964b07152d234b70
组合字符串：test1596254400000202cb962ac59075b964b07152d234b70
sign结果：MD5(组合字符串)=e315cf297826abdeb2092cc57f29f0bf

# 2.短信批量发送接口
## 2.1调用地址
地址：http://{address:port}/sms/api/sendMessageMass
请求方法：POST

## 2.2请求包头定义
Accept:application/json
Content-Type:application/json;charset=utf-8

## 2.3请求参数
参数名 类型 必填 说明
userName String 是 帐号用户名
content String 是 短信内容
phoneList [Array] 是 发送手机号码，JSON数组格式。
timestamp Long 是 当前时间戳，精确到毫秒。例如2020年8月1日12:00:00时间戳为：1596254400000
sign String 是 由以下参数值组合成字符串并计算MD5值，参考详细规则 计算：MD5(userName+timestamp+MD5(password))

## 2.4响应结果
参数名 类型 说明
code Integer 处理结果，0为成功，其他失败，详细参考响应状态码
message String 处理结果描述
msgId Long 当code=0时，系统返回唯一消息Id
smsCount Integer 当code=0时，系统返回消耗计费总数

## 2.5请求示例
发送请求：
POSThttp://{address:port}/sms/api/sendMessageMass
 Accept:application/json
 Content-Type:application/json;charset=utf-8
 {
 "userName":"test",
 "content":"【签名】您的验证码是123456",
 "phoneList": ["13500000001","13500000002","13500000003"],
 "timestamp":1596254400000,
 "sign":"e315cf297826abdeb2092cc57f29f0bf"
 }
响应结果：
{
 "code":0,
 "message":"处理成功",
 "msgId":123456,
 "smsCount":3
 }